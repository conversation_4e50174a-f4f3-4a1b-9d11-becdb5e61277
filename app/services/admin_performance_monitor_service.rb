# frozen_string_literal: true

class AdminPerformanceMonitorService
  def self.monitor_query(description, &block)
    return yield unless Rails.env.development? || Rails.env.staging?

    start_time = Time.current
    sql_count_before = count_sql_queries
    
    result = yield
    
    end_time = Time.current
    sql_count_after = count_sql_queries
    
    duration = ((end_time - start_time) * 1000).round(2)
    query_count = sql_count_after - sql_count_before
    
    log_performance(description, duration, query_count)
    
    result
  end

  def self.log_slow_queries
    return unless Rails.env.development?

    ActiveSupport::Notifications.subscribe('sql.active_record') do |name, start, finish, id, payload|
      duration = (finish - start) * 1000
      
      if duration > 100 # Log queries taking more than 100ms
        Rails.logger.warn "SLOW QUERY (#{duration.round(2)}ms): #{payload[:sql]}"
      end
    end
  end

  private

  def self.count_sql_queries
    @sql_count ||= 0
    
    ActiveSupport::Notifications.subscribe('sql.active_record') do |*args|
      @sql_count += 1
    end
    
    @sql_count
  end

  def self.log_performance(description, duration, query_count)
    status = case
             when duration > 1000
               'SLOW'
             when query_count > 10
               'N+1?'
             else
               'OK'
             end

    Rails.logger.info "ADMIN PERF [#{status}] #{description}: #{duration}ms, #{query_count} queries"
  end
end

# Initialize slow query logging in development
AdminPerformanceMonitorService.log_slow_queries if Rails.env.development?
