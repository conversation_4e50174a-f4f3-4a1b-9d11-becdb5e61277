require 'action_dispatch/http/parameters'
module SuperAdmin
  class BaseController < ApplicationController
    include SessionSecurity
    include AdminErrorHandling
    include AdminLoggingHelpers

    layout 'super_admin'

    before_action :set_current_request_details
    before_action :authenticate
    before_action :check_verification
    before_action :require_onboarding_completion
    before_action :set_current_organization
    before_action :require_organization_selected
    before_action :check_impersonation_restrictions
    before_action :require_admin_access
    after_action :log_admin_request

    private

    def require_admin_access
      # During impersonation, check the original admin user's permissions
      user_to_check =
        if Current.impersonating?
          User.find_by(id: Current.impersonator_id)
        else
          Current.user
        end

      unless user_to_check&.can_access_admin?
        redirect_to root_path,
                    alert: 'Access denied. Administrative privileges required.'
      end
    end

    # Permission checking helpers for controllers
    def require_permission(permission)
      unless Current.user&.can?(permission)
        redirect_to super_admin_root_path,
                    alert: 'Access denied. Insufficient permissions.'
      end
    end

    def require_resource_permission(resource, action)
      permission = "#{resource}_#{action}"
      unless Current.user&.can?(permission)
        redirect_to super_admin_root_path,
                    alert: 'Access denied. Insufficient permissions.'
        return
      end
    end

    def require_superadmin
      unless Current.user&.admin_role == :superadmin
        redirect_to super_admin_root_path,
                    alert: 'Access denied. Super admin privileges required.'
      end
    end

    # Helper method to check if current user can perform action
    def can_perform?(action, resource = nil)
      return false unless Current.user

      if resource
        Current.user.can?("#{resource}_#{action}")
      else
        Current.user.can?(action)
      end
    end

    # Helper to get current user's admin role for views
    def current_admin_role
      Current.user&.admin_role
    end

    def current_admin_permissions
      Current.user&.admin_permissions || []
    end

    # Log admin requests for monitoring and debugging
    def log_admin_request
      return if request.get? && action_name == 'index' # Skip routine index requests

      log_admin_action(
        "#{controller_name}_#{action_name}",
        details: {
          controller: controller_name,
          action: action_name,
          method: request.method,
          path: request.fullpath,
          params: filtered_request_params,
          response_status: response.status,
          processing_time: request_processing_time,
        },
      )
    end

    def filtered_request_params
      filter =
        ActionDispatch::Http::ParameterFilter.new(
          Rails.application.config.filter_parameters,
        )
      filter.filter(params.to_unsafe_h.except('controller', 'action'))
    end

    def request_processing_time
      return nil unless defined?(@_process_action_start_time)

      ((Time.current - @_process_action_start_time) * 1000).round(2)
    end

    def process_action(*args)
      @_process_action_start_time = Time.current
      super
    end

    helper_method :can_perform?, :current_admin_role, :current_admin_permissions
  end
end
