# == Schema Information
#
# Table name: admin_audit_logs
#
#  id            :bigint           not null, primary key
#  action        :string           not null
#  change_data   :json
#  controller    :string           not null
#  ip_address    :string
#  resource_type :string
#  user_agent    :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_user_id :bigint           not null
#  resource_id   :bigint
#
# Indexes
#
#  index_admin_audit_logs_on_action                         (action)
#  index_admin_audit_logs_on_admin_user_id                  (admin_user_id)
#  index_admin_audit_logs_on_admin_user_id_and_created_at   (admin_user_id,created_at)
#  index_admin_audit_logs_on_controller                     (controller)
#  index_admin_audit_logs_on_created_at                     (created_at)
#  index_admin_audit_logs_on_resource                       (resource_type,resource_id)
#  index_admin_audit_logs_on_resource_type_and_created_at   (resource_type,created_at)
#  index_admin_audit_logs_on_resource_type_and_resource_id  (resource_type,resource_id)
#
# Foreign Keys
#
#  fk_rails_...  (admin_user_id => users.id)
#
require 'test_helper'

class AdminAuditLogTest < ActiveSupport::TestCase
  setup do
    # Create roles
    @superadmin_role = Role.find_or_create_by!(name: 'superadmin')
    @scout_role = Role.find_or_create_by!(name: 'scout')

    # Create admin user
    @admin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Admin',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @admin, role: @superadmin_role)
    @admin.reload

    # Create regular user
    @user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Regular',
        last_name: 'User',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: @user, role: @scout_role)
  end

  test 'should create valid audit log' do
    log =
      AdminAuditLog.new(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
        ip_address: '127.0.0.1',
        user_agent: 'Test Browser',
      )

    assert log.valid?
    assert log.save
  end

  test 'should require action' do
    log =
      AdminAuditLog.new(
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    assert_not log.valid?
    assert_includes log.errors[:action], "can't be blank"
  end

  test 'should require controller' do
    log =
      AdminAuditLog.new(action: 'create', resource: @user, admin_user: @admin)

    assert_not log.valid?
    assert_includes log.errors[:controller], "can't be blank"
  end

  test 'should require admin_user' do
    log =
      AdminAuditLog.new(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
      )

    assert_not log.valid?
    assert_includes log.errors[:admin_user], 'must exist'
  end

  test 'should validate action inclusion' do
    log =
      AdminAuditLog.new(
        action: 'invalid_action',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    assert_not log.valid?
    assert_includes log.errors[:action], 'is not included in the list'
  end

  test 'should accept valid actions' do
    AdminAuditLog::ACTIONS.each do |action|
      log =
        AdminAuditLog.new(
          action: action,
          controller: 'AdminUsersController',
          resource: @user,
          admin_user: @admin,
        )

      assert log.valid?, "Action '#{action}' should be valid"
    end
  end

  test 'recent scope returns logs from last 30 days' do
    recent_log =
      AdminAuditLog.create!(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
        created_at: 1.day.ago,
      )

    old_log =
      AdminAuditLog.create!(
        action: 'update',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
        created_at: 35.days.ago,
      )

    recent_logs = AdminAuditLog.recent
    assert_includes recent_logs, recent_log
    assert_not_includes recent_logs, old_log
  end

  test 'by_admin scope filters by admin user' do
    admin_log =
      AdminAuditLog.create!(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    other_admin =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Other',
        last_name: 'Admin',
        onboarding_completed: true,
        onboarding_step: 'completed',
      )
    UserRole.create!(user: other_admin, role: @superadmin_role)

    other_log =
      AdminAuditLog.create!(
        action: 'update',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: other_admin,
      )

    admin_logs = AdminAuditLog.by_admin(@admin.id)
    assert_includes admin_logs, admin_log
    assert_not_includes admin_logs, other_log
  end

  test 'by_action scope filters by action' do
    create_log =
      AdminAuditLog.create!(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    update_log =
      AdminAuditLog.create!(
        action: 'update',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    create_logs = AdminAuditLog.by_action('create')
    assert_includes create_logs, create_log
    assert_not_includes create_logs, update_log
  end

  test 'by_resource_type scope filters by resource type' do
    user_log =
      AdminAuditLog.create!(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    organization = Organization.create!(name: 'Test Org')
    org_log =
      AdminAuditLog.create!(
        action: 'create',
        controller: 'AdminOrganizationsController',
        resource: organization,
        admin_user: @admin,
      )

    user_logs = AdminAuditLog.by_resource_type('User')
    assert_includes user_logs, user_log
    assert_not_includes user_logs, org_log
  end

  test 'by_controller scope filters by controller' do
    users_log =
      AdminAuditLog.create!(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    jobs_log =
      AdminAuditLog.create!(
        action: 'create',
        controller: 'AdminJobsController',
        admin_user: @admin,
      )

    users_logs = AdminAuditLog.by_controller('AdminUsersController')
    assert_includes users_logs, users_log
    assert_not_includes users_logs, jobs_log
  end

  test 'with_changes scope returns logs with changes' do
    log_with_changes =
      AdminAuditLog.create!(
        action: 'update',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )
    log_with_changes.write_attribute(
      :change_data,
      { 'first_name' => %w[Old New] },
    )
    log_with_changes.save!

    log_without_changes =
      AdminAuditLog.create!(
        action: 'read',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    logs_with_changes = AdminAuditLog.with_changes
    assert_includes logs_with_changes, log_with_changes
    assert_not_includes logs_with_changes, log_without_changes
  end

  test 'log_action class method creates audit log' do
    # Mock Current to simulate request context
    Current.define_singleton_method(:user) { @admin }
    Current.define_singleton_method(:ip_address) { '127.0.0.1' }
    Current.define_singleton_method(:user_agent) { 'Test Browser' }

    assert_difference 'AdminAuditLog.count', 1 do
      AdminAuditLog.log_action(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
        changes: {
          'first_name' => %w[Old New],
        },
      )
    end

    log = AdminAuditLog.last
    assert_equal 'create', log.action
    assert_equal 'AdminUsersController', log.controller
    assert_equal @user, log.resource
    assert_equal @admin, log.admin_user
    assert_equal '127.0.0.1', log.ip_address
    assert_equal 'Test Browser', log.user_agent
    assert_equal(
      { 'first_name' => %w[Old New] },
      log.read_attribute(:change_data),
    )
  ensure
    # Clean up the singleton methods
    Current.singleton_class.remove_method(:user) if Current.respond_to?(:user)
    if Current.respond_to?(:ip_address)
      Current.singleton_class.remove_method(:ip_address)
    end
    if Current.respond_to?(:user_agent)
      Current.singleton_class.remove_method(:user_agent)
    end
  end

  test 'log_action does not create log for non-admin users' do
    Current.define_singleton_method(:user) { @user }

    assert_no_difference 'AdminAuditLog.count' do
      AdminAuditLog.log_action(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
      )
    end
  ensure
    Current.singleton_class.remove_method(:user) if Current.respond_to?(:user)
  end

  test 'log_action handles errors gracefully' do
    Current.define_singleton_method(:user) { @admin }
    Current.define_singleton_method(:ip_address) { '127.0.0.1' }
    Current.define_singleton_method(:user_agent) { 'Test Browser' }

    # Create invalid log that will fail validation
    assert_no_difference 'AdminAuditLog.count' do
      AdminAuditLog.log_action(
        action: 'invalid_action',
        controller: 'AdminUsersController',
        resource: @user,
      )
    end
  ensure
    Current.singleton_class.remove_method(:user) if Current.respond_to?(:user)
    if Current.respond_to?(:ip_address)
      Current.singleton_class.remove_method(:ip_address)
    end
    if Current.respond_to?(:user_agent)
      Current.singleton_class.remove_method(:user_agent)
    end
  end

  test 'resource_name returns humanized resource type' do
    log =
      AdminAuditLog.create!(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    assert_equal 'User', log.resource_name
  end

  test 'resource_name returns Unknown for nil resource_type' do
    log =
      AdminAuditLog.create!(
        action: 'export',
        controller: 'AdminUsersController',
        admin_user: @admin,
      )

    assert_equal 'Unknown', log.resource_name
  end

  test 'resource_identifier returns resource info when resource exists' do
    log =
      AdminAuditLog.create!(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    identifier = log.resource_identifier
    assert_includes identifier, @user.id.to_s
    assert_includes identifier, @user.email
  end

  test 'resource_identifier returns fallback when resource is deleted' do
    log =
      AdminAuditLog.create!(
        action: 'delete',
        controller: 'AdminUsersController',
        resource_type: 'User',
        resource_id: 999_999,
        admin_user: @admin,
      )

    identifier = log.resource_identifier
    assert_includes identifier, 'User'
    assert_includes identifier, '999999'
  end

  test 'description returns appropriate message for different actions' do
    # Test create action
    create_log =
      AdminAuditLog.create!(
        action: 'create',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )
    assert_includes create_log.description, 'Created'

    # Test update action
    update_log =
      AdminAuditLog.create!(
        action: 'update',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )
    assert_includes update_log.description, 'Updated'

    # Test delete action
    delete_log =
      AdminAuditLog.create!(
        action: 'delete',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )
    assert_includes delete_log.description, 'Deleted'
  end

  test 'formatted_changes returns formatted changes' do
    log =
      AdminAuditLog.create!(
        action: 'update',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )
    log.write_attribute(
      :change_data,
      {
        'first_name' => ['Old Name', 'New Name'],
        'email' => %w[<EMAIL> <EMAIL>],
      },
    )
    log.save!

    formatted = log.formatted_changes
    assert_includes formatted.keys, 'First name'
    assert_equal 'Old Name', formatted['First name'][:from]
    assert_equal 'New Name', formatted['First name'][:to]
    assert_includes formatted.keys, 'Email'
  end

  test 'formatted_changes returns empty hash when no changes' do
    log =
      AdminAuditLog.create!(
        action: 'read',
        controller: 'AdminUsersController',
        resource: @user,
        admin_user: @admin,
      )

    assert_equal({}, log.formatted_changes)
  end
end
